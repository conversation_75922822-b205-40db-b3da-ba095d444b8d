import React, { createContext } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { useTracker } from "meteor/react-meteor-data";
import { uniq } from "lodash";
import { StudentGroups } from "../api/studentGroups/studentGroups";

function buildUserContext(meteorUser, userStudentGroups = []) {
  if (!meteorUser) {
    return {
      user: null,
      userId: null,
      userOrgId: null,
      userRoles: [],
      currentSiteAccess: {},
      userSiteAccess: [],
      userStudentGroups: []
    };
  }

  const profile = meteorUser.profile || {};
  const siteAccessList = Array.isArray(profile.siteAccess) ? profile.siteAccess.filter(sa => sa.isActive) : [];

  return {
    // TODO(fmazur) - remove maybe when components use direct context props
    user: meteorUser,
    userId: meteorUser._id || null,
    userOrgId: profile.orgid || null,
    // TODO(fmazur) - decide whether we use id or shorter name getRoleName helper or something like this exists
    userRoles: uniq(siteAccessList.map(sa => sa.role)),
    currentSiteAccess: siteAccessList[0] || {},
    userSiteAccess: siteAccessList,
    userStudentGroups
  };
}

export const UserContext = createContext(buildUserContext());

export const UserContextProvider = ({ children, schoolYear, siteId, orgId }) => {
  const userContext = useTracker(() => {
    const meteorUser = Meteor.user();
    const url = window.location.pathname;
    if (Meteor.loggingOut() || url.includes("logout") || url.includes("login")) {
      return buildUserContext(null);
    }

    let userStudentGroups = [];

    // Subscribe to StudentGroupsAssociatedWithUser:BasicData if we have all required parameters
    if (meteorUser && schoolYear && siteId && orgId) {
      const subscription = Meteor.subscribe("StudentGroupsAssociatedWithUser:BasicData", schoolYear, siteId, orgId);
      if (subscription.ready()) {
        userStudentGroups = StudentGroups.find({}, { sort: { name: 1 } }).fetch();
      }
    }

    return buildUserContext(meteorUser, userStudentGroups);
  }, [schoolYear, siteId, orgId]);

  return <UserContext.Provider value={userContext}>{children}</UserContext.Provider>;
};

UserContextProvider.propTypes = {
  children: PropTypes.node,
  schoolYear: PropTypes.number,
  siteId: PropTypes.string,
  orgId: PropTypes.string
};
