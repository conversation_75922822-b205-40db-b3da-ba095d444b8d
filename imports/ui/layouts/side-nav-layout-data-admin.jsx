import React, { useContext, useCallback, useState, useEffect } from "react";
import PropTypes from "prop-types";
import { groupBy } from "lodash";
import { useHistory, useParams, Link } from "react-router-dom";

import { Meteor } from "meteor/meteor";
import { useTracker } from "meteor/react-meteor-data";
import Alert from "react-s-alert";
import { ListGroup } from "react-bootstrap";

import Navigation from "./navigation/navigation.jsx";
import LoadingScreen from "../components/loading-screen/loading-screen.jsx";
import { StudentGroups } from "/imports/api/studentGroups/studentGroups";
import { Sites } from "/imports/api/sites/sites";
import * as utils from "/imports/api/utilities/utilities";
import { AppDataContext } from "../routing/AppDataContext";
import { getMeteorUserSync, getMeteorUserId } from "/imports/api/utilities/utilities";
import { renderFooter } from "../utilities";
import { SchoolYearContext } from "../../contexts/SchoolYearContext.jsx";

function getNavHeading(headerClass, grade) {
  const title = `Grade: ${grade}`;
  const headingClass = `${headerClass} list-group-item`;
  return <span className={headingClass}>{title}</span>;
}

const DataAdminSideNavLayout = ({ site, studentGroups, studentGroupId, orgid, siteId, navName, content }) => {
  const context = useContext(AppDataContext);
  const match = useParams();

  const isActive = useCallback(
    groupId => {
      return studentGroupId === groupId ? " active" : "";
    },
    [studentGroupId]
  );

  const getSideNavItems = useCallback(
    groups => {
      const { manageView = "students" } = match;
      return groups.map(group => (
        <Link
          className={`${isActive(group._id)} list-group-item`}
          key={group._id}
          to={`/data-admin/manage-group/${manageView}/${orgid}/site/${siteId}/${group._id}`}
        >
          {group.name}
        </Link>
      ));
    },
    [match, isActive, orgid, siteId]
  );

  const renderStudentGroupList = useCallback(() => {
    const studentGroupList = groupBy(studentGroups, "grade");

    const entries = Object.entries(studentGroupList);

    entries.sort(([, [classA]], [, [classB]]) => utils.sortByGradeAndName(classA, classB));

    return entries.map(([grade, groups]) => {
      const headerClass = "list-group-header-static";
      return (
        <div key={grade}>
          {getNavHeading(headerClass, grade)}
          {getSideNavItems(groups)}
        </div>
      );
    });
  }, [studentGroups, getSideNavItems]);

  if (!site) {
    return null;
  }

  const isManageSchoolView = context?.routeName === "data-admin-manage-school";
  const isUnarchiveView = context?.routeName === "data-admin-unarchive";

  return (
    <div className="wrapper">
      <Navigation navName={navName} />
      <main>
        <aside className="side-nav">
          <div className="site-selector" data-testid="siteSelectorId">
            {site.name}
          </div>
          bc
          {!isManageSchoolView && !isUnarchiveView && (
            <ListGroup className="student-group-list">{renderStudentGroupList()}</ListGroup>
          )}
        </aside>
        {content}
      </main>
      {renderFooter({ hasSideNav: true })}
      <LoadingScreen />
      <Alert stack={{ limit: 3 }} effect="jelly" position="top-right" offset={30} />
    </div>
  );
};

DataAdminSideNavLayout.propTypes = {
  user: PropTypes.object,
  site: PropTypes.object.isRequired,
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  studentGroupId: PropTypes.string,
  navName: PropTypes.string,
  content: PropTypes.object,
  studentGroups: PropTypes.array,
  history: PropTypes.object,
  match: PropTypes.object
};

const DataAdminSideNavLayoutWithTracker = ({ orgid, siteId, studentGroupId, navName, content }) => {
  const history = useHistory();
  const { schoolYear: contextSchoolYear } = useContext(SchoolYearContext) || {};
  const [resolvedSchoolYear, setResolvedSchoolYear] = useState(contextSchoolYear);

  // Handle async school year resolution
  useEffect(() => {
    const resolveSchoolYear = async () => {
      const userData = getMeteorUserSync();
      if (userData) {
        const currentSchoolYear = await utils.getCurrentSchoolYear(userData, orgid);
        const schoolYear =
          !contextSchoolYear || contextSchoolYear > currentSchoolYear ? currentSchoolYear : contextSchoolYear;
        setResolvedSchoolYear(schoolYear);
      }
    };

    resolveSchoolYear();
  }, [orgid, contextSchoolYear]);

  const { loading, user, site, studentGroups, resolvedStudentGroupId } = useTracker(() => {
    let siteData = {};

    const currentUserId = getMeteorUserId();
    if (!currentUserId) {
      history.push("/login");
      return { loading: true, site: siteData };
    }
    const userData = getMeteorUserSync();
    if (!userData || !resolvedSchoolYear) {
      return { loading: true, site: siteData };
    }

    const studentGroupsHandler = Meteor.subscribe("StudentGroups:PerOrg", orgid);
    const sitesHandler = Meteor.subscribe("Sites", orgid);

    const isLoading = !studentGroupsHandler.ready() || !sitesHandler.ready();

    let studentGroupsData = [];

    if (!isLoading) {
      studentGroupsData = StudentGroups.find(
        { siteId, schoolYear: resolvedSchoolYear },
        { fields: { name: 1, grade: 1 }, sort: { grade: 1, name: 1 } }
      ).fetch();
      siteData = Sites.findOne({ _id: siteId });
    }

    return {
      loading: isLoading,
      user: userData,
      site: siteData,
      studentGroups: studentGroupsData,
      resolvedStudentGroupId: studentGroupId || studentGroupsData[0]?._id
    };
  }, [orgid, siteId, studentGroupId, resolvedSchoolYear, history]);

  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <DataAdminSideNavLayout
      user={user}
      site={site}
      studentGroups={studentGroups}
      studentGroupId={resolvedStudentGroupId}
      orgid={orgid}
      siteId={siteId}
      navName={navName}
      content={content}
    />
  );
};

DataAdminSideNavLayoutWithTracker.propTypes = {
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  studentGroupId: PropTypes.string,
  navName: PropTypes.string,
  content: PropTypes.object
};

export default DataAdminSideNavLayoutWithTracker;
