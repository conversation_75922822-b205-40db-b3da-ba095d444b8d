import intersection from "lodash/intersection";
import { StudentGroups } from "../studentGroups/studentGroups";
import { getCurrentSchoolYear, getMeteorUser, getMeteorUserId } from "./utilities";
import { Users } from "../users/users";
import { StudentGroupEnrollments } from "../studentGroupEnrollments/studentGroupEnrollments";
import { deactivateUsersSiteAccess } from "../users/server/methods";
import { addClassToSite, moveStudentsBetweenGroups } from "./helpers";
import { getTimestampInfo } from "../helpers/getTimestampInfo";

const teacherRole = "arbitraryIdteacher";
const adminRole = "arbitraryIdadmin";

const changeTeacher = async ({ studentGroupId, newTeacherId, lastModified }) => {
  await StudentGroups.updateAsync(studentGroupId, { $set: { ownerIds: [newTeacherId], lastModified } });
};
const changeSecondaryTeachers = async ({ studentGroupId, secondaryTeachers, lastModified }) => {
  await StudentGroups.updateAsync(studentGroupId, { $set: { secondaryTeachers, lastModified } });
};
const changeGroupName = async ({ studentGroupId, studentGroupName, lastModified }) => {
  await StudentGroups.updateAsync(studentGroupId, { $set: { name: studentGroupName, lastModified } });
};
const changeSectionId = async ({ studentGroupId, sectionId, lastModified }) => {
  await StudentGroups.updateAsync(studentGroupId, { $set: { sectionId, lastModified } });
};
export const changeGroupGrade = async ({ studentGroupId, grade, lastModified }) => {
  const currentGroup = await StudentGroups.findOneAsync(studentGroupId);
  const currentDate = new Date();
  // TODO we may need to figure out a better way to manage groups that change grade when new possibilities for managing inactive groups come out
  const newSectionId = `${currentGroup.sectionId}_${currentDate.valueOf()}`;
  await StudentGroups.updateAsync(
    { _id: studentGroupId },
    {
      $set: {
        sectionId: newSectionId,
        isActive: false,
        lastModified
      }
    }
  );
  let newGroupDocument = await StudentGroups.findOneAsync({
    schoolYear: currentGroup.schoolYear,
    orgid: currentGroup.orgid,
    siteId: currentGroup.siteId,
    sectionId: currentGroup.sectionId,
    grade
  });

  if (!newGroupDocument) {
    const newGroup = {
      teacher: currentGroup.ownerIds[0],
      sectionId: currentGroup.sectionId,
      grade,
      name: currentGroup.name
    };

    const newGroupId = await addClassToSite({
      studentGroup: newGroup,
      orgid: currentGroup.orgid,
      siteId: currentGroup.siteId,
      secondaryTeachers: currentGroup.secondaryTeachers || []
    });

    newGroupDocument = await StudentGroups.findOneAsync(newGroupId);
  }

  const studentsEnrollments = await StudentGroupEnrollments.find({
    studentGroupId,
    isActive: true
  }).fetchAsync();
  const students = studentsEnrollments.map(se => ({ _id: se.studentId }));
  await moveStudentsBetweenGroups({
    students,
    previousGroup: currentGroup,
    nextGroup: newGroupDocument
  });

  return newGroupDocument._id;
};

export function getTeachersToAddAndRemoveSiteAccess({
  newTeacherId,
  newSecondaryTeachers = [],
  previousOwnerIds = [],
  previousSecondaryTeachers = []
}) {
  const userIdsLosingAccess = [];
  const userIdsGainingAccess = [];
  const addedSecondaryTeachers = newSecondaryTeachers.filter(teacher => !previousSecondaryTeachers.includes(teacher));
  const removedSecondaryTeachers = previousSecondaryTeachers.filter(teacher => !newSecondaryTeachers.includes(teacher));
  if (newTeacherId) {
    userIdsGainingAccess.push(newTeacherId);
    userIdsLosingAccess.push(...previousOwnerIds);
  }
  if (addedSecondaryTeachers.length) {
    userIdsGainingAccess.push(...addedSecondaryTeachers);
  }
  if (removedSecondaryTeachers.length) {
    userIdsLosingAccess.push(...removedSecondaryTeachers);
  }
  const teachersBothGainingAndLosingAccess = intersection(userIdsGainingAccess, userIdsLosingAccess);
  if (teachersBothGainingAndLosingAccess.length) {
    teachersBothGainingAndLosingAccess.forEach(teacherToRemove => {
      const deleteCount = 1;
      userIdsLosingAccess.splice(userIdsLosingAccess.indexOf(teacherToRemove), deleteCount);
      userIdsGainingAccess.splice(userIdsGainingAccess.indexOf(teacherToRemove), deleteCount);
    });
  }
  return {
    userIdsGainingAccess,
    userIdsLosingAccess
  };
}

function hasActiveAccessToSite(user, siteId, schoolYear) {
  if (!user.profile.siteAccess.length) {
    return false;
  }
  return !!user.profile.siteAccess.find(
    sa =>
      sa.siteId === siteId &&
      sa.isActive === true &&
      ((sa.schoolYear === schoolYear && sa.role === "arbitraryIdteacher") || sa.role === "arbitraryIdadmin")
  );
}

async function addSiteAccessToUser(user, siteId, schoolYear) {
  const { siteAccess } = user.profile;
  const lastModified = await getTimestampInfo(getMeteorUserId(), null, "addSiteAccessToUser");

  // Check if user already has inactive access to this site and school year
  const existingInactiveAccess = siteAccess.find(
    sa => sa.siteId === siteId && sa.schoolYear === schoolYear && !sa.isActive
  );
  if (existingInactiveAccess) {
    await Users.updateAsync(
      { _id: user._id, "profile.siteAccess": { $elemMatch: { siteId, schoolYear } } },
      { $set: { "profile.siteAccess.$.isActive": true, "profile.lastModified": lastModified } }
    );
    return;
  }

  // Check if user already has active access to this site and school year
  const existingActiveAccess = siteAccess.find(
    sa => sa.siteId === siteId && sa.schoolYear === schoolYear && sa.isActive
  );
  if (existingActiveAccess) {
    return; // User already has active access, no need to add
  }

  // Determine if user is an admin based on any existing admin role
  const isAdmin = siteAccess.some(sa => sa.role === adminRole);

  const newSiteAccess = {
    role: isAdmin ? adminRole : teacherRole,
    siteId,
    schoolYear,
    isActive: true,
    isDefault: true
  };

  // Add the new site access to the array
  const updatedSiteAccess = [...siteAccess, newSiteAccess];

  await Users.updateAsync(
    { _id: user._id },
    { $set: { "profile.siteAccess": updatedSiteAccess, "profile.lastModified": lastModified } }
  );
}

async function getTeachersAndAdminsById(userIds) {
  return Users.find(
    {
      _id: { $in: userIds },
      $or: [{ "profile.siteAccess.role": { $in: [teacherRole, adminRole] } }, { "profile.siteAccess": [] }]
    },
    { fields: { profile: 1 } }
  ).fetchAsync();
}

export async function updateUsersSiteAccess({ userIdsGainingAccess, userIdsLosingAccess, orgid, siteId }) {
  const schoolYear = await getCurrentSchoolYear(getMeteorUser(), orgid);
  const allUserDocuments = await getTeachersAndAdminsById([...userIdsGainingAccess, ...userIdsLosingAccess]);
  const usersGainingNewAccess = userIdsGainingAccess
    .map(userId => allUserDocuments.find(user => user._id === userId))
    .filter(user => user?._id && !hasActiveAccessToSite(user, siteId, schoolYear));
  // eslint-disable-next-line no-restricted-syntax
  for await (const user of usersGainingNewAccess) {
    await addSiteAccessToUser(user, siteId, schoolYear);
  }
  const usersToDeactivateSiteAccess = userIdsLosingAccess
    .map(userId => allUserDocuments.find(user => user._id === userId))
    .filter(f => f);
  if (usersToDeactivateSiteAccess.length) {
    await deactivateUsersSiteAccess(usersToDeactivateSiteAccess, siteId, schoolYear);
  }
}

export async function saveGroupData({
  hasPrimaryTeacherChanged,
  studentGroupId,
  newTeacherId,
  secondaryTeachers,
  studentGroupName,
  grade,
  hasGradeChanged,
  siteId,
  sectionId
}) {
  console.log("* LOG * saveGroupData", {
    hasPrimaryTeacherChanged,
    studentGroupId,
    newTeacherId,
    secondaryTeachers,
    studentGroupName,
    grade,
    hasGradeChanged,
    siteId,
    sectionId
  });
  const {
    ownerIds: previousOwnerIds = [],
    secondaryTeachers: previousSecondaryTeachers = [],
    orgid,
    sectionId: currentSectionId
  } = await StudentGroups.findOneAsync(studentGroupId, {
    fields: {
      ownerIds: 1,
      secondaryTeachers: 1,
      orgid: 1,
      sectionId: 1
    }
  });
  const lastModified = await getTimestampInfo(this?.userId || getMeteorUserId(), orgid, "saveGroupData");
  if (hasPrimaryTeacherChanged) {
    await changeTeacher({ studentGroupId, newTeacherId, lastModified });
  }
  await changeSecondaryTeachers({ studentGroupId, secondaryTeachers, lastModified });
  const { userIdsLosingAccess, userIdsGainingAccess } = getTeachersToAddAndRemoveSiteAccess({
    newTeacherId: hasPrimaryTeacherChanged ? newTeacherId : null,
    newSecondaryTeachers: secondaryTeachers,
    previousOwnerIds,
    previousSecondaryTeachers
  });
  if (userIdsLosingAccess.length || userIdsGainingAccess.length) {
    await updateUsersSiteAccess({ userIdsGainingAccess, userIdsLosingAccess, orgid, siteId });
  }
  await changeGroupName({ studentGroupId, studentGroupName, lastModified });
  if (sectionId && currentSectionId !== sectionId) {
    await changeSectionId({ studentGroupId, sectionId });
  }
  if (hasGradeChanged) {
    return changeGroupGrade({ studentGroupId, grade, lastModified });
  }
  return studentGroupId;
}
